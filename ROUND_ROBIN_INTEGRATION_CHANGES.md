# Round-Robin Integration Changes

## Overview
Modified the round-robin rotation system to properly handle multiple vtuber instances instead of the current single-instance approach.

## Problem Analysis
**Before Changes:**
- VUPsRoundRobinSystem created only ONE UserInfoServer instance for ONE character
- Worker functions (`user_info_1_day_worker`, etc.) bypassed the round-robin system
- Manual iteration through all vtubers via `_ensure_vtuber_servers()`
- Round-robin scheduler wasn't actually managing all vtuber instances

**After Changes:**
- Round-robin system creates and manages multiple UserInfoServer instances (one per vtuber)
- Each vtuber instance is registered with unique server identifier (`user_xingtong`, `user_diana`, etc.)
- Tasks are automatically distributed across all vtuber instances
- Legacy worker functions are deprecated

## Key Changes Made

### 1. VUPsRoundRobinSystem (round_robin_integration.py)

**Modified `_initialize_servers()` method:**
- Now creates multiple UserInfoServer instances for all vtubers
- Uses `get_vtuber_list()` to get all available vtubers
- Registers each with unique server_id: `user_{vtuber_name}`

**Modified `_register_decorated_tasks()` method:**
- Registers tasks for each vtuber instance separately
- Creates vtuber-specific TaskDefinition objects
- Uses vtuber-specific server identifiers for task routing

### 2. RoundRobinTaskScheduler (round_robin_scheduler.py)

**Modified initialization:**
- `server_rotation` is now populated dynamically as servers are registered
- `server_load` dict is initialized empty and populated as needed

**Enhanced `register_server()` method:**
- Automatically adds new servers to rotation queue
- Initializes load tracking for new server types

**Updated `get_next_server()` method:**
- Handles dynamic server list instead of hardcoded ['user', 'creator', 'live']
- Improved error handling for empty server rotation

### 3. UserInfoServer (user_info_till_server.py)

**Deprecated legacy functions:**
- `get_vtubers_server_instance()` - marked as deprecated
- `_ensure_vtuber_servers()` - marked as deprecated
- All worker functions (`user_info_1_day_worker`, etc.) - replaced with deprecation warnings

**Preserved decorated methods:**
- Individual task methods with decorators (`@pull_frequency`, `@pull_schedule`) remain unchanged
- These methods now work automatically with the round-robin system

## Architecture Benefits

### 1. Proper Round-Robin Distribution
- Tasks are now truly distributed across all vtuber instances
- Each vtuber gets equal processing time and resources
- Load balancing works correctly

### 2. Scalability
- Easy to add new vtubers - they're automatically included
- No manual worker function updates needed
- Centralized task management

### 3. Consistency
- All vtuber instances use the same task scheduling logic
- Unified error handling and retry mechanisms
- Consistent logging and monitoring

### 4. Maintainability
- Single source of truth for task definitions
- Decorator-based task registration
- Clear separation of concerns

## Migration Notes

### For Existing Code
- Legacy worker functions still exist but log deprecation warnings
- Existing decorated methods continue to work without changes
- Configuration remains backward compatible

### For New Development
- Use the round-robin system instead of manual vtuber iteration
- Add new tasks using decorators on UserInfoServer methods
- Tasks will automatically be scheduled for all vtuber instances

## Example Usage

```python
# Old approach (deprecated)
async def some_worker():
    servers = _ensure_vtuber_servers()  # Deprecated
    for vtuber, server in servers.items():
        await server.some_method()

# New approach (automatic via round-robin)
class UserInfoServer(BaseServer):
    @pull_frequency(minutes=60)
    @use_task_cookie()
    async def some_method(self):
        # This method will be automatically called for each vtuber instance
        # by the round-robin scheduler
        pass
```

## Testing Recommendations

1. **Verify Multiple Instances:** Ensure all vtubers get their own UserInfoServer instance
2. **Check Task Distribution:** Confirm tasks are executed across all vtuber instances
3. **Monitor Load Balancing:** Verify round-robin rotation works correctly
4. **Test Error Handling:** Ensure failures in one vtuber don't affect others
5. **Validate Scheduling:** Check that frequency and cron schedules work per-vtuber

## Future Enhancements

1. **Dynamic Vtuber Management:** Add/remove vtubers without system restart
2. **Per-Vtuber Configuration:** Different schedules/priorities per vtuber
3. **Advanced Load Balancing:** Consider vtuber-specific resource requirements
4. **Monitoring Dashboard:** Real-time view of per-vtuber task execution
