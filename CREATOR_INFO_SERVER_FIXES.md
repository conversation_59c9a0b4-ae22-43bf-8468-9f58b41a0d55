# Creator Info Server Fixes and Optimizations

## Issues Fixed

### 1. Bug in `fetch_weekly_play_analyze()` function
**Location**: `vups_server/server/creator_info_server.py:518`

**Problem**: Incorrect data source for `interact_fans_per` tendency list
```python
# BEFORE (incorrect)
json.dumps(data.get('fan_play', {}).get('tendency_list'))

# AFTER (fixed)
json.dumps(data.get('interact_fans_per', {}).get('tendency_list'))
```

### 2. Missing columns in `weekly_play_analyze_table`
**Location**: `vups_server/sql/sentence/creator_sql.py`

**Problem**: Table schema missing `interact_fans_per` columns that were being inserted

**Added columns**:
- `interact_fans_per_amount`
- `interact_fans_per_amount_pass_per`
- `interact_fans_per_amount_last`
- `interact_fans_per_amount_last_pass_per`
- `interact_fans_per_amount_change`
- `interact_fans_per_amount_med`
- `interact_fans_per_date`
- `interact_fans_per_tendency_list`

### 3. Missing columns in `weekly_attention_analyze_table`
**Location**: `vups_server/sql/sentence/creator_sql.py`

**Problem**: Table schema missing multiple metric columns that were being inserted

**Added columns**:
- `play_trans_fan_per_*` (8 columns)
- `viewer_play_*` (8 columns)
- `arch_fans_num_all_*` (8 columns)

### 4. Parameter count mismatches in INSERT statements
**Problem**: INSERT statements had incorrect parameter counts

**Fixed**:
- `weekly_play_analyze_table`: Updated from 28 to 35 parameters
- `weekly_attention_analyze_table`: Updated from 28 to 47 parameters

## Schema Optimization Recommendations

### Current Issues with Flat Table Design

1. **High Redundancy**: Each metric category repeats the same 8 column pattern:
   - `amount`, `amount_pass_per`, `amount_last`, `amount_last_pass_per`
   - `amount_change`, `amount_med`, `date`, `tendency_list`

2. **Schema Bloat**: Tables have 40+ columns with repetitive structures

3. **Maintenance Overhead**: Adding new metrics requires schema changes

4. **Query Complexity**: Cross-metric analysis requires complex JOINs

### Proposed Normalized Design

**File**: `vups_server/sql/sentence/creator_sql_optimized.py`

#### Core Table: `weekly_metrics_table`
```sql
CREATE TABLE weekly_metrics_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    report_type varchar(50) NOT NULL,    -- 'overview', 'play_analyze', 'attention_analyze'
    metric_category varchar(50) NOT NULL, -- 'play_cnt', 'all_play', 'net_attention_cnt'
    amount bigint,
    amount_pass_per bigint,
    amount_last bigint,
    amount_last_pass_per bigint,
    amount_change bigint,
    amount_med bigint,
    date_value bigint,
    tendency_list jsonb,
    additional_data jsonb,               -- For extra fields like 'tip'
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, report_type, metric_category, date_value)
);
```

#### Benefits of Normalized Design

1. **Reduced Redundancy**: 
   - Single table structure for all metrics
   - ~80% reduction in column count
   - Consistent data patterns

2. **Improved Performance**:
   - Targeted indexes on key columns
   - Better cache utilization
   - Faster queries for specific metrics

3. **Enhanced Maintainability**:
   - No schema changes for new metrics
   - Single validation point
   - Easier to understand and debug

4. **Better Analytics**:
   - Cross-metric analysis simplified
   - Flexible aggregation options
   - Easier reporting queries

#### Backward Compatibility

Views are provided to maintain compatibility with existing queries:
- `weekly_overview_view`
- `weekly_play_analyze_view`
- `weekly_attention_analyze_view`

#### Migration Strategy

1. **Phase 1**: Create normalized tables alongside existing ones
2. **Phase 2**: Update application to write to both schemas
3. **Phase 3**: Migrate historical data using provided scripts
4. **Phase 4**: Update queries to use new views
5. **Phase 5**: Remove old tables after validation

## Implementation Status

✅ **Fixed immediate bugs**:
- Corrected data source bug in `creator_info_server.py`
- Added missing columns to both tables
- Fixed parameter count mismatches in INSERT statements

✅ **Created optimized schema design**:
- Normalized table structure
- Performance indexes
- Backward-compatible views
- Migration scripts

## Next Steps

1. **Test the fixes**: Verify that the corrected functions work properly
2. **Consider optimization**: Evaluate implementing the normalized schema
3. **Performance testing**: Compare query performance between approaches
4. **Gradual migration**: If adopting normalized design, implement gradually

## Files Modified

- `vups_server/server/creator_info_server.py` (1 line fix)
- `vups_server/sql/sentence/creator_sql.py` (schema updates)
- `vups_server/sql/sentence/creator_sql_optimized.py` (new optimized design)
