# Optimized Schema Design for Creator Analytics
# This design reduces redundancy and improves maintainability

# --- Normalized metric data table ---
create_weekly_metrics_table_sql = """CREATE TABLE IF NOT EXISTS weekly_metrics_table (
    id serial4 PRIMARY KEY,
    uid varchar(50) NOT NULL,
    report_type varchar(50) NOT NULL, -- 'overview', 'play_analyze', 'attention_analyze'
    metric_category varchar(50) NOT NULL, -- 'play_cnt', 'all_play', 'net_attention_cnt', etc.
    amount bigint,
    amount_pass_per bigint,
    amount_last bigint,
    amount_last_pass_per bigint,
    amount_change bigint,
    amount_med bigint,
    date_value bigint,
    tendency_list jsonb,
    additional_data jsonb, -- For any extra fields like 'tip'
    create_time bigint,
    update_time timestamp,
    UNIQUE (uid, report_type, metric_category, date_value)
);"""

# Index for better query performance
create_weekly_metrics_indexes_sql = """
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_uid_report ON weekly_metrics_table(uid, report_type);
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_category ON weekly_metrics_table(metric_category);
CREATE INDEX IF NOT EXISTS idx_weekly_metrics_date ON weekly_metrics_table(date_value);
"""

# Insert statement for normalized metrics
insert_weekly_metrics_table_sql = """INSERT INTO weekly_metrics_table (
    uid, report_type, metric_category, amount, amount_pass_per, amount_last, amount_last_pass_per,
    amount_change, amount_med, date_value, tendency_list, additional_data, create_time, update_time
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14
) ON CONFLICT (uid, report_type, metric_category, date_value) DO UPDATE SET
    amount = EXCLUDED.amount,
    amount_pass_per = EXCLUDED.amount_pass_per,
    amount_last = EXCLUDED.amount_last,
    amount_last_pass_per = EXCLUDED.amount_last_pass_per,
    amount_change = EXCLUDED.amount_change,
    amount_med = EXCLUDED.amount_med,
    tendency_list = EXCLUDED.tendency_list,
    additional_data = EXCLUDED.additional_data,
    update_time = EXCLUDED.update_time;
"""

# --- Views for easier querying ---
create_weekly_overview_view_sql = """CREATE OR REPLACE VIEW weekly_overview_view AS
SELECT 
    uid,
    date_value,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount END) as play_cnt_amount,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_pass_per END) as play_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_last END) as play_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'play_cnt' THEN amount_change END) as play_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount END) as interact_rate_amount,
    MAX(CASE WHEN metric_category = 'interact_rate' THEN amount_pass_per END) as interact_rate_amount_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount END) as net_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_pass_per END) as net_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount END) as interact_fans_per_amount,
    MAX(CASE WHEN metric_category = 'avs_num' THEN amount END) as avs_num_amount,
    MAX(update_time) as update_time
FROM weekly_metrics_table 
WHERE report_type = 'overview'
GROUP BY uid, date_value;
"""

create_weekly_play_analyze_view_sql = """CREATE OR REPLACE VIEW weekly_play_analyze_view AS
SELECT 
    uid,
    date_value,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount END) as all_play_amount,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_pass_per END) as all_play_amount_pass_per,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_last END) as all_play_amount_last,
    MAX(CASE WHEN metric_category = 'all_play' THEN amount_change END) as all_play_amount_change,
    MAX(CASE WHEN metric_category = 'all_play' THEN tendency_list END) as all_play_tendency_list,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount END) as viewer_play_amount,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount_change END) as viewer_play_amount_change,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount END) as fan_play_amount,
    MAX(CASE WHEN metric_category = 'fan_play' THEN amount_change END) as fan_play_amount_change,
    MAX(CASE WHEN metric_category = 'interact_fans_per' THEN amount END) as interact_fans_per_amount,
    MAX(CASE WHEN metric_category = 'all_play' THEN additional_data->>'tip' END) as tip,
    MAX(update_time) as update_time
FROM weekly_metrics_table 
WHERE report_type = 'play_analyze'
GROUP BY uid, date_value;
"""

create_weekly_attention_analyze_view_sql = """CREATE OR REPLACE VIEW weekly_attention_analyze_view AS
SELECT 
    uid,
    date_value,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount END) as net_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_pass_per END) as net_attention_cnt_amount_pass_per,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_last END) as net_attention_cnt_amount_last,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN amount_change END) as net_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount END) as new_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'new_attention_cnt' THEN amount_change END) as new_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount END) as un_attention_cnt_amount,
    MAX(CASE WHEN metric_category = 'un_attention_cnt' THEN amount_change END) as un_attention_cnt_amount_change,
    MAX(CASE WHEN metric_category = 'play_trans_fan_per' THEN amount END) as play_trans_fan_per_amount,
    MAX(CASE WHEN metric_category = 'viewer_play' THEN amount END) as viewer_play_amount,
    MAX(CASE WHEN metric_category = 'arch_fans_num_all' THEN amount END) as arch_fans_num_all_amount,
    MAX(CASE WHEN metric_category = 'net_attention_cnt' THEN additional_data->>'tip' END) as tip,
    MAX(update_time) as update_time
FROM weekly_metrics_table 
WHERE report_type = 'attention_analyze'
GROUP BY uid, date_value;
"""

# --- Migration script to convert existing data ---
migrate_existing_data_sql = """
-- This script would migrate data from the old flat tables to the new normalized structure
-- Example for weekly_key_data_overview_table:

INSERT INTO weekly_metrics_table (uid, report_type, metric_category, amount, amount_pass_per, amount_last, amount_last_pass_per, amount_change, amount_med, date_value, tendency_list, create_time, update_time)
SELECT 
    uid, 'overview', 'play_cnt', play_cnt_amount, play_cnt_amount_pass_per, play_cnt_amount_last, play_cnt_amount_last_pass_per, play_cnt_amount_change, play_cnt_amount_med, play_cnt_date, play_cnt_tendency_list, create_time, update_time
FROM weekly_key_data_overview_table
UNION ALL
SELECT 
    uid, 'overview', 'interact_rate', interact_rate_amount, interact_rate_amount_pass_per, interact_rate_amount_last, interact_rate_amount_last_pass_per, interact_rate_amount_change, interact_rate_amount_med, interact_rate_date, interact_rate_tendency_list, create_time, update_time
FROM weekly_key_data_overview_table
-- ... continue for all metric categories
ON CONFLICT (uid, report_type, metric_category, date_value) DO NOTHING;
"""

# --- Benefits of this approach ---
"""
BENEFITS OF NORMALIZED SCHEMA:

1. REDUCED REDUNDANCY:
   - Single table stores all metric types with consistent structure
   - Eliminates duplicate column patterns across tables
   - Reduces storage space and maintenance overhead

2. IMPROVED QUERY PERFORMANCE:
   - Targeted indexes on commonly queried columns
   - Better cache utilization due to smaller table size
   - Easier to add new metric types without schema changes

3. ENHANCED DATA INTEGRITY:
   - Consistent data types and constraints across all metrics
   - Single point of validation for metric data
   - Easier to implement business rules and constraints

4. SIMPLIFIED SCHEMA MANAGEMENT:
   - Adding new metric categories requires no schema changes
   - Views provide backward compatibility with existing queries
   - Easier to maintain and understand codebase

5. BETTER ANALYTICS CAPABILITIES:
   - Cross-metric analysis becomes simpler
   - Easier to implement aggregations and reporting
   - More flexible querying options

MIGRATION STRATEGY:
1. Create new normalized tables alongside existing ones
2. Update application code to write to both old and new tables
3. Migrate historical data using the migration script
4. Update queries to use new views
5. Remove old tables once migration is complete
"""
